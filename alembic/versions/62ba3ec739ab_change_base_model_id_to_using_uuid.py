"""change base model id to using uuid

Revision ID: 62ba3ec739ab
Revises: 1e64a76c8ed9
Create Date: 2025-03-02 14:46:17.059849

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '62ba3ec739ab'
down_revision: Union[str, None] = '1e64a76c8ed9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create a temporary column for the new UUID
    op.add_column('user', sa.Column('new_id', sa.String(36)))
    
    # Update the temporary column with UUIDs generated in Python
    import uuid
    conn = op.get_bind()
    result = conn.execute(sa.text('SELECT id FROM "user"'))
    for row in result:
        conn.execute(
            sa.text('UPDATE "user" SET new_id = :uuid WHERE id = :old_id'),
            parameters={"uuid": str(uuid.uuid4()), "old_id": row[0]}
        )
    
    # Drop the old primary key constraint
    op.drop_constraint('user_pkey', 'user', type_='primary')
    
    # Drop the old id column
    op.drop_column('user', 'id')
    
    # Rename new_id to id
    op.alter_column('user', 'new_id', new_column_name='id')
    
    # Make the new id column the primary key
    op.create_primary_key('user_pkey', 'user', ['id'])
    
    # Add index on id column
    op.create_index(op.f('ix_user_id'), 'user', ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create temporary column for integer ids
    op.add_column('user', sa.Column('new_id', sa.Integer()))
    
    # Generate sequential integers
    conn = op.get_bind()
    conn.execute(
        sa.text(
            """
            UPDATE "user" 
            SET new_id = id_sequence.id 
            FROM (
                SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as id_sequence 
                FROM "user"
            ) as id_sequence 
            WHERE "user".id = id_sequence.id
            """
        )
    )
    
    # Drop the old primary key constraint and index
    op.drop_constraint('user_pkey', 'user', type_='primary')
    op.drop_index(op.f('ix_user_id'), 'user')
    
    # Drop the old id column
    op.drop_column('user', 'id')
    
    # Rename new_id to id
    op.alter_column('user', 'new_id', new_column_name='id')
    
    # Make the new id column the primary key
    op.create_primary_key('user_pkey', 'user', ['id'])
    # ### end Alembic commands ###
