#!/bin/bash

CUR_DIR=$(pwd)
# Default values
CONTAINER_NAME="e-commerce-postgres"
POSTGRES_PASSWORD="1234"  # Default to no password
POSTGRES_USER="postgres"
POSTGRES_DB="postgres"
POSTGRES_PORT=5432
DEFAULT_MOUNTED_DATA_DIR="/Users/<USER>/Workspace/projects/thingstore-data/.postgres-data" # Default directory on the host machine
MOUNTED_DATA_DIR="$DEFAULT_MOUNTED_DATA_DIR"
IMAGE_NAME="postgres:17.2-alpine"

# Usage function
usage() {
  echo """Usage: $0 
    [-n container_name] \
    [-u postgres_user]
    [-p postgres_password]
    [-d postgres_db]
    [-P postgres_port]
    [-m mounted_data_dir]
    [-i postgres_image_name]
  """
}

is_help_showed=false

# Parse command-line arguments
while [ "$#" -gt 0 ]; do
  case "$1" in
    -n) CONTAINER_NAME="$2"; shift 2 ;;  # Skip next argument as it's the value
    -u) POSTGRES_USER="$2"; shift 2 ;;
    -p) POSTGRES_PASSWORD="$2"; shift 2 ;;
    -d) POSTGRES_DB="$2"; shift 2 ;;
    -P) POSTGRES_PORT="$2"; shift 2 ;;
    -m) MOUNTED_DATA_DIR="$2"; shift 2 ;;
    -i) IMAGE_NAME="$2"; shift 2 ;;
    -h) is_help_showed=true; usage; break ;;
    *) echo "Unknown option: $1"; is_help_showed=true; usage; break ;;
  esac
done

if [ "$MOUNTED_DATA_DIR" == "$DEFAULT_MOUNTED_DATA_DIR" ]; then
  MOUNTED_DATA_DIR="$DEFAULT_MOUNTED_DATA_DIR/$CONTAINER_NAME"
fi

if [ "$is_help_showed" == false ]; then
  # Run the Docker container
  docker run -d \
    --name "$CONTAINER_NAME" \
    -e POSTGRES_DB="$POSTGRES_DB" \
    -e POSTGRES_USER="$POSTGRES_USER" \
    -e POSTGRES_PASSWORD="$POSTGRES_PASSWORD" \
    -e PGDATA=/var/lib/postgresql/data/pgdata \
    -p "$POSTGRES_PORT":5432 \
    -v "$MOUNTED_DATA_DIR:/var/lib/postgresql/data" \
    "$IMAGE_NAME"

  # Output container details
  echo "PostgreSQL container '$CONTAINER_NAME' is running:"
  echo "- Image: $IMAGE_NAME"
  echo "- User: $POSTGRES_USER"
  echo "- Database: $POSTGRES_DB"
  echo "- Port: $POSTGRES_PORT"
  echo "- Data Directory (on host): $MOUNTED_DATA_DIR"
fi
