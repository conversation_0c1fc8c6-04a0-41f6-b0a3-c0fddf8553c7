from fastapi import APIRouter, Depends, HTTPException
from app.core.auth.auth_handler import verify_jwt_token
from app.schemas.users import UserResponse
from app.models.users import User

from sqlalchemy.ext.asyncio import AsyncSession
from app.db.base import get_db

from sqlalchemy.future import select

from typing import Annotated
from uuid import UUID
from app.core.auth.auth_dependencies import get_current_active_user
router = APIRouter()

@router.get("/me")
async def read_users_me(current_user: Annotated[User, Depends(get_current_active_user)]):
    return current_user

@router.get("/{user_id}", response_model=UserResponse)
async def get_user_by_id(user_id: UUID, _user: User = Depends(verify_jwt_token), db: AsyncSession = Depends(get_db)):
    record = await db.execute(select(User).where(User.id == user_id))
    user = record.scalars().first()

    if not user:
        raise HTTPException(status_code=404, detail=f"User with ID {user_id} not found")

    return user

@router.get("", response_model=list[UserResponse])
async def get_users(_user: User = Depends(verify_jwt_token), db: AsyncSession = Depends(get_db)):
    record = await db.execute(select(User).where(User.is_active))
    users = record.scalars().all()
    print(users)
    return users
