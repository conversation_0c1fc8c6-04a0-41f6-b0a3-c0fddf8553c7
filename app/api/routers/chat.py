# Deprecated

from fastapi import APIRouter, HTTPException
from ..models.chat import ChatMessage, ChatRequest
from typing import List

router = APIRouter(
    prefix="/chat",
    tags=["chat"]
)

@router.post("/", response_model=ChatMessage)
async def chat_with_codebase(request: ChatRequest):
    try:
        # Here you would implement your chat logic
        # This is a simple example response
        response = ChatMessage(
            role="assistant",
            content="I'm here to help you with your codebase. What would you like to know?"
        )
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 