from pydantic import BaseModel, EmailStr, UUID4

from typing import Optional
from datetime import datetime
class UserResponse(BaseModel):
    username: str
    email: str
    is_active: bool

class UserFilter(BaseModel):
    id: Optional[UUID4] = None
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    created_at_start: Optional[datetime] = None
    created_at_end: Optional[datetime] = None
    updated_at_start: Optional[datetime] = None
    updated_at_end: Optional[datetime] = None