from enum import Enum
import os
from dotenv import load_dotenv
from pydantic_settings import BaseSettings


BASE_DIR = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
load_dotenv(os.path.join(BASE_DIR, ".env"))


# TODO: Move to a common constant somewhere
class Environment(Enum):
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TEST = "test"


class BaseConfig(BaseSettings):
    DEBUG: bool = True
    ENVIRONMENT: Environment

    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "PROJECT_NAME")
    API_PREFIX: str = ""
    BACKEND_CORS_ORIGINS: list = ["*"]
    LOGGING_CONFIG_FILE: str = os.path.join(BASE_DIR, "logging.ini")

    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_PORT: str = os.getenv("DB_PORT", "5432")
    DB_NAME: str = os.getenv("DB_NAME", "postgres")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "postgres")

    SECRET_KEY: str = os.getenv("SECRET_KEY", "things_store_app")
    TOKEN_HASH_ALGORITHM: str = os.getenv("TOKEN_HASH_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES_HOUR", 2)

    @property
    def DATABASE_URL(self) -> str:
        return "postgresql+asyncpg://{username}:{password}@{host}:{port}/{database}".format(
            username=self.DB_USER,
            password=self.DB_PASSWORD,
            host=self.DB_HOST,
            port=self.DB_PORT,
            database=self.DB_NAME,
        )
