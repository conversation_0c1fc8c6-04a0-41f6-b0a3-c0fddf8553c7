from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
import datetime

from jose import jwt, J<PERSON><PERSON>rro<PERSON>
from fastapi import HTTPException, Depends
from fastapi.security import <PERSON>A<PERSON>2<PERSON><PERSON>wordBearer

from app.core.configs import settings

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/token")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict, expires_delta: datetime.timedelta | None = None):
    """Generate a JWT token for the user."""

    payload = data.copy()
    if expires_delta:
        expire = datetime.datetime.now(datetime.UTC)+ expires_delta
    else:
        expire = datetime.datetime.now(datetime.UTC) + datetime.timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    payload.update({"exp": expire})
    return jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.TOKEN_HASH_ALGORITHM)

def verify_jwt_token(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.TOKEN_HASH_ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
