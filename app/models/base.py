import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, func, Uuid
from sqlalchemy.orm import declarative_base

Base = declarative_base()


class BaseModel(Base):
    __abstract__ = (
        True  # This ensures SQLAlchemy doesn't create a table for this model
    )

    id = Column(
        Uuid,
        primary_key=True,
        index=True,
        default=lambda: str(uuid.uuid4()),
    )
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        server_onupdate=func.now(),
    )

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        if not hasattr(cls, "__tablename__"):
            cls.__tablename__ = (
                cls.__name__.lower()
            )  # Automatically set the table name to the class name in lowercase


class IsDeletedMixin:
    """
    TODO: implement all related func to Soft delete later.
    Considers reference to https://github.com/flipbit03/sqlalchemy-easy-softdelete

    Mixin class to add soft delete functionality to a SQLAlchemy model.

    This mixin adds an `is_deleted` column to indicate whether a record
    is considered deleted, and a `deleted_at` column to store the
    timestamp of when the record was marked as deleted.

    Attributes:
        is_deleted (bool): A flag indicating if the record is deleted.
        deleted_at (datetime): The timestamp when the record was deleted.
    """

    is_deleted = Column(Boolean, default=False)
    deleted_at = created_at = Column(DateTime(timezone=True))
