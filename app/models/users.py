from sqlalchemy import Column, Integer, String, Boolean
from sqlalchemy.orm import relationship

from .base import BaseModel

class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)

    cart_items = relationship(
        "CartItem", back_populates="user", cascade="all, delete"
    )

    def __repr__(self):
        return f"User(username={self.username}, email={self.email})"
