from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, Uuid
from sqlalchemy.orm import relationship

from ..base import BaseModel


class CartItem(BaseModel):
    __tablename__ = "cart_items"

    quantity = Column(Integer, default=1)

    user_id = Column(Uuid, ForeignKey("users.id"))
    product_variant_id = Column(Uuid, ForeignKey("product_variants.id"))

    user = relationship("User", back_populates="cart_items")
    product_variant = relationship(
        "ProductVariant", back_populates="cart_items"
    )

    def __repr__(self) -> str:
        return f"CartItem(user_id={self.user_id!r}, product_variant_id={self.product_variant_id!r}, quantity={self.quantity!r})"
