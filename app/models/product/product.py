from decimal import Decimal

from sqlalchemy import JSON, Column, Foreign<PERSON>ey, Integer, Numeric, String, Uuid
from sqlalchemy.orm import relationship

from ..base import BaseModel


class Product(BaseModel):
    __tablename__ = "products"

    name = Column(String, index=True, nullable=False)
    description = Column(String)

    brand_id = Column(Uuid, ForeignKey("brands.id"))
    category_id = Column(Uuid, ForeignKey("categories.id"))

    brand = relationship("Brand", back_populates="products")
    category = relationship("Category", back_populates="products")
    variants = relationship(
        "ProductVariant", back_populates="product", cascade="all, delete"
    )

    def __repr__(self):
        return f"Product(name={self.name}, brand={self.brand}, category={self.category})"


class ProductVariant(BaseModel):
    __tablename__ = "product_variants"

    product_id = Column(Uuid, ForeignKey("products.id"))
    # SKU (Stock Keeping Unit) is a unique identifier for each product variant
    # Used for inventory tracking and management
    # Example: NIKE-AF1-WHT-42 (Brand-Model-Color-Size)
    sku = Column(String, unique=True, index=True)
    # Variant name like "Nike Air Force 1 - White/42"
    name = Column(String, index=True)
    price = Column(Numeric(20, 2), default=Decimal("0.00"))
    stock = Column(Integer, default=0)
    # Variant attributes (e.g. size, color etc)
    # Example: {"size": "42", "color": "white", "material": "leather"}
    attributes = Column(JSON, nullable=True, default={})

    # Relationships
    product = relationship("Product", back_populates="variants")
    cart_items = relationship(
        "CartItem", back_populates="product_variant", cascade="all, delete"
    )

    def __repr__(self):
        return f"ProductVariant(name={self.name}, sku={self.sku}, product_id={self.product_id}, price={self.price})"
