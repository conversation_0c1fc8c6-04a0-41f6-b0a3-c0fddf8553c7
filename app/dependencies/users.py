from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.models.users import User
from app.db.base import get_db



async def get_user_by_params(filters: dict, db: AsyncSession):
    query = select(User)

    # Define a mapping of filter keys to SQLAlchemy conditions
    filter_conditions = {
        "id": User.id,
        "email": User.email,
        "username": User.username,
        "is_active": User.is_active,
        "created_at_start": lambda v: User.created_at >= v,
        "created_at_end": lambda v: User.created_at <= v,
        "updated_at_start": lambda v: User.updated_at >= v,
        "updated_at_end": lambda v: User.updated_at <= v,
    }

    # Apply filters dynamically using list comprehension
    conditions = [
        condition(filters[key]) if callable(condition) else condition == filters[key]
        for key, condition in filter_conditions.items()
        if key in filters
    ]

    if conditions:
        query = query.where(*conditions)

    result = await db.execute(query)
    return result.scalars().first()