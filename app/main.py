from fastapi import FastAPI
import uvicorn

from app.core.configs import settings
from app.api import router


# from app.api.routers.endpoints import users, items
def get_app():

    app = FastAPI(
        title=settings.PROJECT_NAME,
        openapi_url=f"{settings.API_PREFIX}/openapi.json",
        docs_url="/docs",
        redoc_url="/re-docs",
    )
    app.include_router(router, prefix=settings.API_PREFIX)

    return app

app = get_app()


if __name__ == '__main__':
    uvicorn.run(app, host="0.0.0.0", port=8000)
